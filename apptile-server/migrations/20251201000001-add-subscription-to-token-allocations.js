'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Add subscriptionId column to organization_token_allocations table
    await queryInterface.addColumn('organization_token_allocations', 'subscriptionId', {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'subscriptions',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });

    // Create index on subscriptionId
    await queryInterface.addIndex('organization_token_allocations', ['subscriptionId']);
  },

  down: async (queryInterface, Sequelize) => {
    // Remove index
    await queryInterface.removeIndex('organization_token_allocations', ['subscriptionId']);

    // Remove column
    await queryInterface.removeColumn('organization_token_allocations', 'subscriptionId');

    // Note: We cannot remove enum values in PostgreSQL easily, so we leave the 'subscription' value
  }
};
