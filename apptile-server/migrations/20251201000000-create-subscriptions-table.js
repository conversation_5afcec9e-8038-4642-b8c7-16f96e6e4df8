'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Create subscriptions table
    await queryInterface.createTable('subscriptions', {
      id: {
        type: Sequelize.UUID,
        allowNull: false,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      organizationId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'organizations',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      paddleSubscriptionId: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      paddlePlanId: {
        type: Sequelize.STRING,
        allowNull: false
      },
      status: {
        type: Sequelize.ENUM('active', 'cancelled', 'expired', 'pending', 'paused'),
        allowNull: false,
        defaultValue: 'pending'
      },
      billingInterval: {
        type: Sequelize.ENUM('monthly', 'yearly'),
        allowNull: false
      },
      tokenAllocation: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: 'Number of tokens allocated per billing period'
      },
      currentPeriodStart: {
        type: Sequelize.DATE,
        allowNull: false
      },
      currentPeriodEnd: {
        type: Sequelize.DATE,
        allowNull: false
      },
      nextBillingDate: {
        type: Sequelize.DATE,
        allowNull: false
      },
      cancelAtPeriodEnd: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      trialEnd: {
        type: Sequelize.DATE,
        allowNull: true
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create indexes
    await queryInterface.addIndex('subscriptions', ['organizationId']);
    await queryInterface.addIndex('subscriptions', ['paddleSubscriptionId']);
    await queryInterface.addIndex('subscriptions', ['status']);
    await queryInterface.addIndex('subscriptions', ['nextBillingDate']);
  },

  down: async (queryInterface, Sequelize) => {
    // Drop indexes first
    await queryInterface.removeIndex('subscriptions', ['organizationId']);
    await queryInterface.removeIndex('subscriptions', ['paddleSubscriptionId']);
    await queryInterface.removeIndex('subscriptions', ['status']);
    await queryInterface.removeIndex('subscriptions', ['nextBillingDate']);

    // Drop table
    await queryInterface.dropTable('subscriptions');

    // Drop ENUMs
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_subscriptions_status";');
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_subscriptions_billingInterval";');
  }
};
